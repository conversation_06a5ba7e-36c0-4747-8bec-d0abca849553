'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tslib_es6 = require('../../tslib.es6-7a681263.cjs');
var actions_resize_ResizePadAction = require('./ResizePadAction.cjs');
require('../../internal/qualifier/Qualifier.cjs');
require('../../internal/qualifier/QualifierValue.cjs');
require('../../internal/models/QualifierModel.cjs');
require('../../internal/models/qualifierToJson.cjs');
require('../../internal/utils/unsupportedError.cjs');
require('./ResizeAdvancedAction.cjs');
require('./ResizeSimpleAction.cjs');
require('../../internal/Action.cjs');
require('../../qualifiers/flag/FlagQualifier.cjs');
require('../../internal/utils/dataStructureUtils.cjs');
require('../../internal/models/ActionModel.cjs');
require('../../internal/models/actionToJson.cjs');
require('../../internal/utils/toFloatAsString.cjs');
require('../../qualifiers/aspectRatio/AspectRatioQualifierValue.cjs');
require('../../qualifiers/flag.cjs');
require('../../internal/internalConstants.cjs');
require('../../internal/utils/objectFlip.cjs');
require('../../internal/models/createGravityModel.cjs');
require('../../qualifiers/gravity/autoGravity/AutoGravity.cjs');
require('../../qualifiers/gravity/GravityQualifier.cjs');
require('../../qualifiers/gravity/focusOnGravity/FocusOnGravity.cjs');
require('../../qualifiers/gravity.cjs');
require('../../qualifiers/gravity/compassGravity/CompassGravity.cjs');
require('../../qualifiers/gravity/xyCenterGravity/XYCenterGravity.cjs');
require('../../qualifiers/gravity/qualifiers/focusOn/FocusOnValue.cjs');
require('../../internal/models/createGravityFromModel.cjs');
require('../../qualifiers/focusOn.cjs');
require('../../qualifiers/autoFocus.cjs');
require('../../qualifiers/gravity/qualifiers/compass/CompassQualifier.cjs');
require('../../internal/models/createBackgroundModel.cjs');
require('../../qualifiers/background/shared/BlurredBackgroundAction.cjs');
require('../../qualifiers/background/shared/base/BackgroundQualifier.cjs');
require('../../qualifiers/background/shared/auto/BackgroundAutoBorderQualifier.cjs');
require('../../qualifiers/background/shared/base/BaseCommonBackground.cjs');
require('../../internal/utils/prepareColor.cjs');
require('../../qualifiers/background/shared/gradient/BackgroundBorderGradientQualifier.cjs');
require('../../qualifiers/background/shared/base/BaseGradientBackground.cjs');
require('../../qualifiers/background/shared/gradient/BackgroundPredominantGradientQualifier.cjs');
require('../../qualifiers/background/shared/auto/BackgroundAutoPredominantQualifier.cjs');
require('../../qualifiers/background/shared/BackgroundGenerativeFillQualifier.cjs');
require('../../internal/utils/encodePromptComponents.cjs');
require('../../internal/models/createBackgroundFromModel.cjs');
require('../../qualifiers/rotate/RotationModeQualifierValue.cjs');
require('../../qualifiers/region/CustomRegion.cjs');
require('../../qualifiers/region/NamedRegion.cjs');
require('../../qualifiers/region/RectangleRegion.cjs');
require('../../qualifiers/position/PositionQualifier.cjs');
require('../../qualifiers/gradientDirection/GradientDirectionQualifierValue.cjs');
require('../../qualifiers/format/FormatQualifier.cjs');
require('../../qualifiers/expression/ExpressionQualifier.cjs');
require('../../qualifiers/background.cjs');
require('../../qualifiers/animatedFormat/AnimatedFormatQualifierValue.cjs');
require('../../qualifiers/source/sourceTypes/VideoSource.cjs');
require('../../qualifiers/source/BaseSource.cjs');
require('../../qualifiers/source/sourceTypes/ImageSource.cjs');
require('../../qualifiers/source/sourceTypes/SubtitlesSource.cjs');
require('../../qualifiers/source/sourceTypes/BaseTextSource.cjs');
require('../../qualifiers/textStyle.cjs');
require('../../qualifiers/fontWeight.cjs');
require('../../qualifiers/fontStyle.cjs');
require('../../qualifiers/textDecoration.cjs');
require('../../internal/utils/serializeCloudinaryCharacters.cjs');
require('../../qualifiers/textStroke.cjs');
require('../../internal/models/IStrokeModel.cjs');
require('../../qualifiers/source/sourceTypes/FetchSource.cjs');
require('../../internal/utils/base64Encode.cjs');
require('../../qualifiers/source/sourceTypes/TextSource.cjs');
require('../../internal/models/createTextStyleFromModel.cjs');
require('../../qualifiers/source/sourceTypes/AudioSource.cjs');

/**
 * @description Defines an advanced resize with limit padding.
 * @extends Actions.Resize.ResizePadAction
 * @memberOf Actions.Resize
 * @see Visit {@link Actions.Resize| Resize} for examples
 */
var ResizeLimitPadAction = /** @class */ (function (_super) {
    tslib_es6.__extends(ResizeLimitPadAction, _super);
    function ResizeLimitPadAction() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return ResizeLimitPadAction;
}(actions_resize_ResizePadAction.ResizePadAction));

exports.ResizeLimitPadAction = ResizeLimitPadAction;
