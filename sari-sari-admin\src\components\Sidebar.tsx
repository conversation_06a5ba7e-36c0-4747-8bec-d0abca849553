'use client'

import { BarChart3, History, Calendar, Settings } from 'lucide-react'
import { useTheme } from 'next-themes'

interface SidebarProps {
  activeSection: string
  setActiveSection: (section: string) => void
}

export default function Sidebar({ activeSection, setActiveSection }: SidebarProps) {
  const { resolvedTheme } = useTheme()
  const menuItems = [
    {
      id: 'api-graphing',
      label: 'API Graphing & Visuals',
      icon: BarChart3,
      description: 'ECharts analytics and data visualization'
    },
    {
      id: 'history',
      label: 'History',
      icon: History,
      description: 'Transaction and activity logs'
    },
    {
      id: 'calendar',
      label: 'Calendar',
      icon: Calendar,
      description: 'Events and scheduling'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      description: 'Store configuration and preferences'
    },
  ]

  return (
    <div
      className="w-80 bg-white dark:bg-slate-800 shadow-lg border-r border-gray-200 dark:border-slate-700 transition-colors duration-300 sticky top-16 h-[calc(100vh-4rem)] overflow-y-auto sidebar-scroll"
      style={{
        backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
        borderColor: resolvedTheme === 'dark' ? '#475569' : '#e5e7eb'
      }}
    >
      <div className="flex flex-col h-full">
        {/* Header Section */}
        <div
          className="p-6 transition-colors duration-300"
          style={{
            borderBottom: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #f3f4f6'
          }}
        >
          <h2
            className="text-lg font-semibold mb-2 transition-colors duration-300"
            style={{
              color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
            }}
          >
            Additional Tools
          </h2>
          <p
            className="text-sm transition-colors duration-300"
            style={{
              color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
            }}
          >
            Advanced features and utilities
          </p>
        </div>

        {/* Navigation Section - Scrollable */}
        <nav className="flex-1 px-4 py-4 space-y-2">
        {menuItems.map((item) => {
          const Icon = item.icon
          const isActive = activeSection === item.id

          return (
            <button
              key={item.id}
              onClick={() => setActiveSection(item.id)}
              className={`w-full flex items-start p-4 text-left rounded-xl transition-all duration-200 group hover:scale-[1.02] ${
                isActive
                  ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 border border-green-200 dark:border-green-800 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-slate-700 border border-transparent hover:shadow-md'
              }`}
            >
              <div className={`p-2 rounded-lg mr-3 ${
                isActive
                  ? 'bg-green-100 dark:bg-green-900/40'
                  : 'bg-gray-100 dark:bg-slate-600 group-hover:bg-gray-200 dark:group-hover:bg-slate-500'
              }`}>
                <Icon className={`h-5 w-5 ${
                  isActive
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-gray-500 dark:text-gray-400'
                }`} />
              </div>
              <div className="flex-1">
                <h3
                  className="font-medium text-sm transition-colors duration-300"
                  style={{
                    color: isActive
                      ? (resolvedTheme === 'dark' ? '#4ade80' : '#16a34a')
                      : (resolvedTheme === 'dark' ? '#f8fafc' : '#111827')
                  }}
                >
                  {item.label}
                </h3>
                <p
                  className="text-xs mt-1 transition-colors duration-300"
                  style={{
                    color: isActive
                      ? (resolvedTheme === 'dark' ? '#22c55e' : '#15803d')
                      : (resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280')
                  }}
                >
                  {item.description}
                </p>
              </div>
            </button>
          )
        })}
        </nav>

        {/* Footer Section - Always at bottom */}
        <div
          className="mt-auto p-6 transition-colors duration-300"
          style={{
            borderTop: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #e5e7eb',
            backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#f9fafb'
          }}
        >
        <div
          className="text-sm transition-colors duration-300"
          style={{
            color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
          }}
        >
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-6 h-6 hero-gradient rounded-md flex items-center justify-center">
              <span className="text-white font-bold text-xs">R</span>
            </div>
            <span
              className="font-medium transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#f8fafc' : '#374151'
              }}
            >
              Revantad Store
            </span>
          </div>
          <p className="text-xs">Admin Dashboard v2.0</p>
          <p className="text-xs">Professional Business Management</p>
        </div>
        </div>
      </div>
    </div>
  )
}
