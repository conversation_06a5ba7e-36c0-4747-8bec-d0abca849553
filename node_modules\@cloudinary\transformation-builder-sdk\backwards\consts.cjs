'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var LEGACY_CONDITIONAL_OPERATORS = {
    "=": 'eq',
    "!=": 'ne',
    "<": 'lt',
    ">": 'gt',
    "<=": 'lte',
    ">=": 'gte',
    "&&": 'and',
    "||": 'or',
    "*": "mul",
    "/": "div",
    "+": "add",
    "-": "sub",
    "^": "pow"
};
var CF_SHARED_CDN = "d3jpl91pxevbkh.cloudfront.net";
var OLD_AKAMAI_SHARED_CDN = "cloudinary-a.akamaihd.net";
var AKAMAI_SHARED_CDN = "res.cloudinary.com";
var SHARED_CDN = AKAMAI_SHARED_CDN;
var LEGACY_PREDEFINED_VARS = {
    "aspect_ratio": "ar",
    "aspectRatio": "ar",
    "current_page": "cp",
    "currentPage": "cp",
    "duration": "du",
    "face_count": "fc",
    "faceCount": "fc",
    "height": "h",
    "initial_aspect_ratio": "iar",
    "initial_height": "ih",
    "initial_width": "iw",
    "initialAspectRatio": "iar",
    "initialHeight": "ih",
    "initialWidth": "iw",
    "initial_duration": "idu",
    "initialDuration": "idu",
    "page_count": "pc",
    "page_x": "px",
    "page_y": "py",
    "pageCount": "pc",
    "pageX": "px",
    "pageY": "py",
    "tags": "tags",
    "width": "w"
};
var NUMBER_PATTERN = "([0-9]*)\\.([0-9]+)|([0-9]+)";
var OFFSET_ANY_PATTERN = "(" + NUMBER_PATTERN + ")([%pP])?";
var RANGE_VALUE_RE = RegExp("^" + OFFSET_ANY_PATTERN + "$");
var OFFSET_ANY_PATTERN_RE = RegExp("(" + OFFSET_ANY_PATTERN + ")\\.\\.(" + OFFSET_ANY_PATTERN + ")");
var LAYER_KEYWORD_PARAMS = {
    font_weight: "normal",
    font_style: "normal",
    text_decoration: "none",
    text_align: '',
    stroke: "none"
};

exports.AKAMAI_SHARED_CDN = AKAMAI_SHARED_CDN;
exports.CF_SHARED_CDN = CF_SHARED_CDN;
exports.LAYER_KEYWORD_PARAMS = LAYER_KEYWORD_PARAMS;
exports.LEGACY_CONDITIONAL_OPERATORS = LEGACY_CONDITIONAL_OPERATORS;
exports.LEGACY_PREDEFINED_VARS = LEGACY_PREDEFINED_VARS;
exports.NUMBER_PATTERN = NUMBER_PATTERN;
exports.OFFSET_ANY_PATTERN = OFFSET_ANY_PATTERN;
exports.OFFSET_ANY_PATTERN_RE = OFFSET_ANY_PATTERN_RE;
exports.OLD_AKAMAI_SHARED_CDN = OLD_AKAMAI_SHARED_CDN;
exports.RANGE_VALUE_RE = RANGE_VALUE_RE;
exports.SHARED_CDN = SHARED_CDN;
