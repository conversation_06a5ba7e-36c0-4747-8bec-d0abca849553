import { LeveledEffectAction } from "../EffectActions/LeveledEffectAction.js";
/**
 * @description Converts the image to black and white.
 * @extends LeveledEffectAction
 * @memberOf Actions.Effect
 * @see Visit {@link Actions.Effect|Effect} for an example
 */
declare class BlackwhiteEffectAction extends LeveledEffectAction {
    threshold(value: number | string): this;
}
export { BlackwhiteEffectAction };
