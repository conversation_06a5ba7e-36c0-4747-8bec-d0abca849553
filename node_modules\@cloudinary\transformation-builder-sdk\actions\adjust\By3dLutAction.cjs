'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tslib_es6 = require('../../tslib.es6-7a681263.cjs');
var internal_Action = require('../../internal/Action.cjs');
require('../../qualifiers/flag/FlagQualifier.cjs');
require('../../internal/qualifier/QualifierValue.cjs');
require('../../internal/qualifier/Qualifier.cjs');
require('../../internal/models/QualifierModel.cjs');
require('../../internal/models/qualifierToJson.cjs');
require('../../internal/utils/unsupportedError.cjs');
require('../../internal/utils/dataStructureUtils.cjs');
require('../../internal/models/ActionModel.cjs');
require('../../internal/models/actionToJson.cjs');

/**
 * @description Creates the 3D_lut layer transformation
 * @memberOf Actions.Adjust
 * @extends SDK.Action
 */
var By3dLutAction = /** @class */ (function (_super) {
    tslib_es6.__extends(By3dLutAction, _super);
    function By3dLutAction(publicId) {
        var _this = _super.call(this) || this;
        _this.publicId = publicId;
        return _this;
    }
    /**
     * Returns a string representation of the action
     * @return {string}
     */
    By3dLutAction.prototype.toString = function () {
        return "l_lut:" + this.publicId + "/fl_layer_apply";
    };
    return By3dLutAction;
}(internal_Action.Action));

exports.By3dLutAction = By3dLutAction;
