'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var tslib_es6 = require('../../tslib.es6-7a681263.cjs');
var actions_resize_ResizeSimpleAction = require('./ResizeSimpleAction.cjs');
require('../../internal/qualifier/Qualifier.cjs');
require('../../internal/qualifier/QualifierValue.cjs');
require('../../internal/models/QualifierModel.cjs');
require('../../internal/models/qualifierToJson.cjs');
require('../../internal/utils/unsupportedError.cjs');
require('../../internal/Action.cjs');
require('../../qualifiers/flag/FlagQualifier.cjs');
require('../../internal/utils/dataStructureUtils.cjs');
require('../../internal/models/ActionModel.cjs');
require('../../internal/models/actionToJson.cjs');
require('../../internal/utils/toFloatAsString.cjs');
require('../../qualifiers/aspectRatio/AspectRatioQualifierValue.cjs');
require('../../qualifiers/flag.cjs');
require('../../internal/internalConstants.cjs');
require('../../internal/utils/objectFlip.cjs');

/**
 * @description Defines a limit fitting resize action.
 * @extends Actions.Resize.ResizeSimpleAction
 * @memberOf Actions.Resize
 * @see Visit {@link Actions.Resize| Resize} for examples
 */
var ResizeLimitFitAction = /** @class */ (function (_super) {
    tslib_es6.__extends(ResizeLimitFitAction, _super);
    function ResizeLimitFitAction() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return ResizeLimitFitAction;
}(actions_resize_ResizeSimpleAction.ResizeSimpleAction));

exports.ResizeLimitFitAction = ResizeLimitFitAction;
