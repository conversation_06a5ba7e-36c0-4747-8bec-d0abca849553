'use strict';

var tslib_es6 = require('../../tslib.es6-7a681263.cjs');
var backwards_legacyLayer_textlayer = require('./textlayer.cjs');
require('./layer.cjs');
require('../utils/snakeCase.cjs');
require('../utils/isEmpty.cjs');
require('../utils/smartEscape.cjs');
require('../utils/isNumberLike.cjs');

var SubtitlesLayer = /** @class */ (function (_super) {
    tslib_es6.__extends(SubtitlesLayer, _super);
    /**
     * Represent a subtitles layer
     * @constructor SubtitlesLayer
     * @param {Object} options - layer parameters
     */
    function SubtitlesLayer(options) {
        var _this = _super.call(this, options) || this;
        _this.options.resourceType = "subtitles";
        return _this;
    }
    return SubtitlesLayer;
}(backwards_legacyLayer_textlayer));

module.exports = SubtitlesLayer;
