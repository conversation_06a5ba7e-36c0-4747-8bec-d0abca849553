import { Action } from "../../internal/Action.js";
import { IActionModel } from "../../internal/models/IActionModel.js";
import { IDprModel } from "../../internal/models/IDeliveryActionModel.js";
/**
 * @description Specifies the dpr.
 * @memberOf Actions.Delivery
 * @extends SDK.Action
 * @see Visit {@link Actions.Delivery|Delivery} for an example
 */
declare class DeliveryDPRAction extends Action {
    protected _actionModel: IDprModel;
    /**
     * Create a new DeliveryDPRAction
     * @param dprValue
     */
    constructor(dprValue: string | number);
    static fromJson(actionModel: IActionModel): DeliveryDPRAction;
}
export { DeliveryDPRAction };
