import { Action } from "../../internal/Action.js";
import { IStreamingProfileActionModel } from "../../internal/models/ITranscodeActionModel.js";
import { IActionModel } from "../../internal/models/IActionModel.js";
/**
 * @extends SDK.Action
 * @memberOf Actions.Transcode
 * @description The predefined streaming profiles.
 *
 * <b>Learn more</b>: {@link https://cloudinary.com/documentation/adaptive_bitrate_streaming#predefined_streaming_profiles|Predefined streaming profiles}
 * @see Visit {@link Actions.Transcode|Transcode} for an example
 */
declare class StreamingProfileAction extends Action {
    protected _actionModel: IStreamingProfileActionModel;
    constructor(profile: string | number);
    static from<PERSON>son(actionModel: IActionModel): StreamingProfileAction;
}
export default StreamingProfileAction;
