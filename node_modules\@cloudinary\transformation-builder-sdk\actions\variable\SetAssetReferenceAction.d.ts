import VariableAction from "./VariableAction.js";
/**
 * @description Sets a user-defined variable from a file reference.
 * @memberOf Actions.Variable
 * @extends {Variable.VariableAction}
 * @see Visit {@link Actions.Variable|Variable} for an example
 */
declare class SetAssetReferenceAction extends VariableAction {
    constructor(name: string, value: string);
}
export default SetAssetReferenceAction;
