import { LeveledEffectAction } from "../EffectActions/LeveledEffectAction.js";
import { IActionModel } from "../../../internal/models/IActionModel.js";
/**
 * @description Delivers a video or animated GIF that contains additional loops of the video/GIF.
 * @extends LeveledEffectAction
 * @memberOf Actions.Effect
 * @see Visit {@link Actions.Effect|Effect} for an example
 */
declare class LoopEffectAction extends LeveledEffectAction {
    additionalIterations(value: number | string): this;
    static from<PERSON>son(actionModel: IActionModel): LoopEffectAction;
}
export { LoopEffectAction };
