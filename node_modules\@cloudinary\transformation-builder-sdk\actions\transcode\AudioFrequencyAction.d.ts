import { Action } from "../../internal/Action.js";
import { IAudioFrequencyActionModel } from "../../internal/models/ITranscodeActionModel.js";
import { IActionModel } from "../../internal/models/IActionModel.js";
/**
 * @extends SDK.Action
 * @memberOf Actions.Transcode
 * @description Controls audio sample frequency.
 *
 * <b>Learn more</b>: {@link https://cloudinary.com/documentation/audio_transformations#audio_codec_settings|Audio frequency control}
 * @see Visit {@link Actions.Transcode|Transcode} for an example
 */
declare class AudioFrequencyAction extends Action {
    protected _actionModel: IAudioFrequencyActionModel;
    constructor(freq: string | number);
    static from<PERSON>son(actionModel: IActionModel): AudioFrequencyAction;
}
export default AudioFrequencyAction;
