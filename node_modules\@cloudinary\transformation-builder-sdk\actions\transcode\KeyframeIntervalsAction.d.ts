import { Action } from "../../internal/Action.js";
import { IKeyframeIntervalsActionModel } from "../../internal/models/ITranscodeActionModel.js";
import { IActionModel } from "../../internal/models/IActionModel.js";
/**
 * @extends SDK.Action
 * @memberOf Actions.Transcode
 * @description Controls the keyframe interval of the delivered video.
 * @see Visit {@link Actions.Transcode|Transcode} for an example
 */
declare class KeyframeIntervalsAction extends Action {
    protected _actionModel: IKeyframeIntervalsActionModel;
    constructor(interval: number | string);
    static from<PERSON>son(actionModel: IActionModel): KeyframeIntervalsAction;
}
export default KeyframeIntervalsAction;
