import { Action } from "../../internal/Action.js";
import { IActionModel } from "../../internal/models/IActionModel.js";
import { IDeliveryColorSpaceFromICCActionModel } from "../../internal/models/IDeliveryActionModel.js";
/**
 * @description Specifies the ICC profile to use for the color space.
 * @memberOf Actions.Delivery
 * @extends SDK.Action
 * @see Visit {@link Actions.Delivery|Delivery} for an example
 */
declare class DeliveryColorSpaceFromICCAction extends Action {
    protected _actionModel: IDeliveryColorSpaceFromICCActionModel;
    /**
     * @param {string} publicId
     */
    constructor(publicId: string);
    static from<PERSON>son(actionModel: IActionModel): DeliveryColorSpaceFromICCAction;
}
export { DeliveryColorSpaceFromICCAction };
