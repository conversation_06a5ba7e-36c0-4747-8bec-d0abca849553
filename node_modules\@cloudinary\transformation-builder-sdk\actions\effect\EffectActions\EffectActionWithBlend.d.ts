import { LeveledEffectAction } from "./LeveledEffectAction.js";
/**
 * @description A class for all effects that include a blendPercentage method
 * @extends {Actions.Effect.LeveledEffectAction}
 * @memberOf Actions.Effect
 * @see Visit {@link Actions.Effect|Effect} for an example
 */
declare class EffectActionWithBlend extends LeveledEffectAction {
    blend(value: number | string): this;
}
export { EffectActionWithBlend };
