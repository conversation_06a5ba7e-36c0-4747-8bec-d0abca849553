import VariableAction from "./VariableAction.js";
/**
 * @description Sets a user-defined variable from contextual metadata.
 * @memberOf Actions.Variable
 * @extends {Variable.VariableAction}
 * @see Visit {@link Actions.Variable|Variable} for an example
 */
declare class SetFromContextAction extends VariableAction {
    constructor(name: string, value: string);
}
export default SetFromContextAction;
