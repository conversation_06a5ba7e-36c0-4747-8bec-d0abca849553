import { LeveledEffectAction } from "./LeveledEffectAction.js";
/**
 * @description A class for all effects that include a strength method
 * @extends {Actions.Effect.LeveledEffectAction}
 * @memberOf Actions.Effect
 * @see Visit {@link Actions.Effect|Effect} for an example
 */
declare class EffectActionWithStrength extends LeveledEffectAction {
    LEVEL_NAME: string;
    strength(value: number | string): this;
}
export { EffectActionWithStrength };
