export const LEGACY_CONDITIONAL_OPERATORS = {
    "=": 'eq',
    "!=": 'ne',
    "<": 'lt',
    ">": 'gt',
    "<=": 'lte',
    ">=": 'gte',
    "&&": 'and',
    "||": 'or',
    "*": "mul",
    "/": "div",
    "+": "add",
    "-": "sub",
    "^": "pow"
};
export const CF_SHARED_CDN = "d3jpl91pxevbkh.cloudfront.net";
export const OLD_AKAMAI_SHARED_CDN = "cloudinary-a.akamaihd.net";
export const AKAMAI_SHARED_CDN = "res.cloudinary.com";
export const SHARED_CDN = AKAMAI_SHARED_CDN;
export const LEGACY_PREDEFINED_VARS = {
    "aspect_ratio": "ar",
    "aspectRatio": "ar",
    "current_page": "cp",
    "currentPage": "cp",
    "duration": "du",
    "face_count": "fc",
    "faceCount": "fc",
    "height": "h",
    "initial_aspect_ratio": "iar",
    "initial_height": "ih",
    "initial_width": "iw",
    "initialAspectRatio": "iar",
    "initialHeight": "ih",
    "initialWidth": "iw",
    "initial_duration": "idu",
    "initialDuration": "idu",
    "page_count": "pc",
    "page_x": "px",
    "page_y": "py",
    "pageCount": "pc",
    "pageX": "px",
    "pageY": "py",
    "tags": "tags",
    "width": "w"
};
export const NUMBER_PATTERN = "([0-9]*)\\.([0-9]+)|([0-9]+)";
export const OFFSET_ANY_PATTERN = `(${NUMBER_PATTERN})([%pP])?`;
export const RANGE_VALUE_RE = RegExp(`^${OFFSET_ANY_PATTERN}$`);
export const OFFSET_ANY_PATTERN_RE = RegExp(`(${OFFSET_ANY_PATTERN})\\.\\.(${OFFSET_ANY_PATTERN})`);
export const LAYER_KEYWORD_PARAMS = {
    font_weight: "normal",
    font_style: "normal",
    text_decoration: "none",
    text_align: '',
    stroke: "none"
};
